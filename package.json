{"name": "my-vue-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "less": "^4.3.0", "mockjs": "^1.1.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}